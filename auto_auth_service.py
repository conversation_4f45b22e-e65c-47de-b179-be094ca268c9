#!/usr/bin/env python3
"""
🔐 Auto Authentication Service
خدمة المصادقة التلقائية

This service handles automatic authentication for Blogger when connection fails.
"""

import os
import json
import asyncio
import time
import subprocess
import webbrowser
from datetime import datetime
from pathlib import Path
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from utils.logger import logger

SCOPES = ['https://www.googleapis.com/auth/blogger']

class AutoAuthService:
    """Automatic authentication service"""
    
    def __init__(self):
        self.credentials = None
        self.flow = None
        self.auth_server_port = 8080
        
    def check_client_secret(self):
        """Check if client_secret.json exists"""
        if os.path.exists('client_secret.json'):
            logger.info("✅ client_secret.json found")
            return True
        else:
            logger.error("❌ client_secret.json not found")
            return False
    
    def load_existing_credentials(self):
        """Load existing credentials from .env"""
        try:
            from config import GOOGLE_OAUTH_TOKEN
            
            if not GOOGLE_OAUTH_TOKEN:
                return None
            
            if isinstance(GOOGLE_OAUTH_TOKEN, str):
                token_data = json.loads(GOOGLE_OAUTH_TOKEN)
            else:
                token_data = GOOGLE_OAUTH_TOKEN
            
            creds = Credentials.from_authorized_user_info(token_data, SCOPES)
            
            if creds and creds.valid:
                logger.info("✅ Valid credentials found")
                self.credentials = creds
                return creds
            elif creds and creds.expired and creds.refresh_token:
                logger.info("🔄 Refreshing expired credentials...")
                try:
                    creds.refresh(Request())
                    logger.info("✅ Credentials refreshed")
                    self.credentials = creds
                    self.save_credentials_to_env()
                    return creds
                except Exception as e:
                    logger.error(f"❌ Failed to refresh: {e}")
                    return None
            else:
                logger.warning("⚠️ Invalid credentials")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error loading credentials: {e}")
            return None
    
    def start_auth_flow(self):
        """Start OAuth flow"""
        try:
            if not self.check_client_secret():
                return False, "client_secret.json not found"
            
            # Create flow
            self.flow = InstalledAppFlow.from_client_secrets_file(
                'client_secret.json', SCOPES)
            
            # Configure for local server
            self.flow.redirect_uri = f'http://localhost:{self.auth_server_port}'
            
            logger.info("✅ OAuth flow initialized")
            return True, "Flow ready"
            
        except Exception as e:
            logger.error(f"❌ Failed to start flow: {e}")
            return False, str(e)
    
    def get_auth_url(self):
        """Get authorization URL"""
        try:
            if not self.flow:
                success, message = self.start_auth_flow()
                if not success:
                    return None, message
            
            auth_url, _ = self.flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true'
            )
            
            logger.info("✅ Authorization URL generated")
            return auth_url, "URL ready"
            
        except Exception as e:
            logger.error(f"❌ Failed to get auth URL: {e}")
            return None, str(e)
    
    def complete_auth_with_code(self, auth_code):
        """Complete authentication with authorization code"""
        try:
            if not self.flow:
                return False, "Flow not initialized"
            
            # Exchange code for credentials
            self.flow.fetch_token(code=auth_code)
            self.credentials = self.flow.credentials
            
            # Save to .env
            self.save_credentials_to_env()
            
            logger.info("✅ Authentication completed")
            return True, "Authentication successful"
            
        except Exception as e:
            logger.error(f"❌ Auth completion failed: {e}")
            return False, str(e)
    
    def save_credentials_to_env(self):
        """Save credentials to .env file"""
        try:
            if not self.credentials:
                return False
            
            # Convert to dict
            token_data = {
                'token': self.credentials.token,
                'refresh_token': self.credentials.refresh_token,
                'token_uri': self.credentials.token_uri,
                'client_id': self.credentials.client_id,
                'client_secret': self.credentials.client_secret,
                'scopes': self.credentials.scopes,
                'expiry': self.credentials.expiry.isoformat() if self.credentials.expiry else None
            }
            
            token_json = json.dumps(token_data)
            
            # Update .env file
            env_lines = []
            if os.path.exists('.env'):
                with open('.env', 'r', encoding='utf-8') as f:
                    env_lines = f.readlines()
            
            # Find and update token line
            token_updated = False
            for i, line in enumerate(env_lines):
                if line.startswith('GOOGLE_OAUTH_TOKEN=') or line.startswith('# Blogger OAuth'):
                    env_lines[i] = f'GOOGLE_OAUTH_TOKEN={token_json}\n'
                    token_updated = True
                    break
            
            if not token_updated:
                env_lines.append(f'GOOGLE_OAUTH_TOKEN={token_json}\n')
            
            # Write back
            with open('.env', 'w', encoding='utf-8') as f:
                f.writelines(env_lines)
            
            logger.info("✅ Credentials saved to .env")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to save credentials: {e}")
            return False
    
    def test_blogger_connection(self):
        """Test connection to Blogger"""
        try:
            from config import BLOG_ID
            from googleapiclient.discovery import build
            
            if not BLOG_ID:
                return False, "BLOG_ID not configured"
            
            if not self.credentials:
                creds = self.load_existing_credentials()
                if not creds:
                    return False, "No valid credentials"
            
            # Build service and test
            service = build('blogger', 'v3', credentials=self.credentials)
            blog = service.blogs().get(blogId=BLOG_ID).execute()
            
            blog_name = blog.get('name', 'Unknown')
            logger.info(f"✅ Connected to blog: {blog_name}")
            return True, f"Connected to: {blog_name}"
            
        except Exception as e:
            logger.error(f"❌ Blogger test failed: {e}")
            return False, str(e)
    
    def auto_authenticate_interactive(self):
        """Interactive authentication process"""
        print("🔐 Starting automatic authentication process...")
        
        # Check existing credentials first
        creds = self.load_existing_credentials()
        if creds:
            success, message = self.test_blogger_connection()
            if success:
                print("✅ Existing authentication is valid!")
                return True
        
        print("⚠️ Authentication needed. Starting OAuth flow...")
        
        # Start flow
        success, message = self.start_auth_flow()
        if not success:
            print(f"❌ Failed to start auth flow: {message}")
            return False
        
        # Get auth URL
        auth_url, message = self.get_auth_url()
        if not auth_url:
            print(f"❌ Failed to get auth URL: {message}")
            return False
        
        print(f"🔗 Opening browser for authentication...")
        print(f"URL: {auth_url}")
        
        # Open browser
        try:
            webbrowser.open(auth_url)
        except:
            print("⚠️ Could not open browser automatically")
        
        # Get authorization code from user
        print("\n📝 After authorizing, you'll get a code.")
        auth_code = input("Enter the authorization code: ").strip()
        
        if not auth_code:
            print("❌ No code provided")
            return False
        
        # Complete authentication
        success, message = self.complete_auth_with_code(auth_code)
        if success:
            print("✅ Authentication completed successfully!")
            
            # Test connection
            success, message = self.test_blogger_connection()
            if success:
                print(f"✅ Blogger connection test: {message}")
                return True
            else:
                print(f"⚠️ Blogger test failed: {message}")
                return False
        else:
            print(f"❌ Authentication failed: {message}")
            return False
    
    async def monitor_and_auto_fix(self):
        """Monitor connection and auto-fix if needed"""
        logger.info("🔍 Starting connection monitor...")
        
        while True:
            try:
                # Test connection
                success, message = self.test_blogger_connection()
                
                if success:
                    logger.info("✅ Blogger connection healthy")
                else:
                    logger.warning(f"⚠️ Blogger connection failed: {message}")
                    
                    # Try to fix automatically
                    logger.info("🔧 Attempting automatic fix...")
                    
                    # Try refreshing credentials
                    creds = self.load_existing_credentials()
                    if creds:
                        success, message = self.test_blogger_connection()
                        if success:
                            logger.info("✅ Connection restored!")
                        else:
                            logger.error("❌ Auto-fix failed. Manual intervention needed.")
                    else:
                        logger.error("❌ No valid credentials. Manual authentication needed.")
                
                # Wait before next check
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"❌ Monitor error: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error

def main():
    """Main function for standalone usage"""
    print("🔐 Auto Authentication Service")
    print("=" * 50)
    
    service = AutoAuthService()
    
    print("1. 🔍 Check current authentication")
    print("2. 🔐 Start interactive authentication")
    print("3. 🧪 Test Blogger connection")
    print("4. 🔍 Start connection monitor")
    print("5. 🚪 Exit")
    
    choice = input("\nChoose option (1-5): ").strip()
    
    if choice == '1':
        creds = service.load_existing_credentials()
        if creds:
            print("✅ Valid credentials found")
        else:
            print("❌ No valid credentials")
    
    elif choice == '2':
        success = service.auto_authenticate_interactive()
        if success:
            print("🎉 Authentication completed successfully!")
        else:
            print("❌ Authentication failed")
    
    elif choice == '3':
        success, message = service.test_blogger_connection()
        print(f"Test result: {message}")
    
    elif choice == '4':
        print("🔍 Starting connection monitor... (Press Ctrl+C to stop)")
        try:
            asyncio.run(service.monitor_and_auto_fix())
        except KeyboardInterrupt:
            print("\n⏹️ Monitor stopped")
    
    elif choice == '5':
        print("👋 Goodbye!")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
